`define SIMULATION       // define SIM<PERSON><PERSON><PERSON>ON to speed up simulation
module breathLED(sysclk, rstn, out, mode, load, cycle, period, step, top, bottom, flash_on_time, flash_off_time, flash_count);
input  sysclk, rstn;     
input  [1:0] mode;       // 0: ALL OFF, 1: Breath, 2: Flash, 3: ALL ON
input  load;             // on 'load' rising edge, update xxxx_value regs with cycle/value/step interface value
input  [15:0] cycle;     // C pwm cycle max counts, pwm pulse freq F = [sysclk/(C+1)^2]
input  [15:0] period;    // P every P+1 pwm cycles, update pwm_duty once, single dir breath freq F0=F/(P+1)
input  [15:0] step;      // S how much add to pwm duty once P pwm cycles elapsed, breath freq F1= (S+1)*F0=(S+1)*F/(P+1)=sysclk*(S+1)/(C+1)^2*(P+1)
input  [15:0] top;       // TOP waiting time at 100% duty cycle, in PWM cycles
input  [15:0] bottom;    // BOT waiting time at 0% duty cycle, in PWM cycles
input  [15:0] flash_on_time;   // Flash mode ON time, in PWM cycles
input  [15:0] flash_off_time;  // Flash mode OFF time, in PWM cycles
input  [7:0] flash_count;      // Flash count, complete cycles
output reg out;          // output connect to led

`ifndef SIMULATION       // NORMAL work
parameter CYCLE_INIT = 500-1;    // C counts of each pwm cycle, pwm freq F=sysclk/[(C+1)^2]=125MHz/250000=500Hz
parameter PERIOD_INIT = 1000-1;  // P one dir breath Time=1/F0 e.g. F0=F/(P+1)=F/1000 = 0.5Hz
parameter STEP_INIT = 1-1;       // S duty cycle update step, one dir breath freq F1=(S+1)*F0, for this instance freq=0.5s
parameter TOP_INIT = 500-1;      // TOP default waiting time at 100% duty cycle (500 PWM cycles)
parameter BOT_INIT = 500-1;      // BOT default waiting time at 0% duty cycle (500 PWM cycles)
parameter FLASH_ON_INIT = 250-1; // Flash ON time default (250 PWM cycles)
parameter FLASH_OFF_INIT = 250-1; // Flash OFF time default (250 PWM cycles)
parameter FLASH_COUNT_INIT = 5-1; // Flash count default (5 complete cycles)
`else                    // IN SIMULATION TOOLS
parameter CYCLE_INIT = 100-1;
parameter PERIOD_INIT = 10-1;
parameter STEP_INIT = 1-1;
parameter TOP_INIT = 10-1;
parameter BOT_INIT = 50-1;       // Increased for simulation too
parameter FLASH_ON_INIT = 5-1;   // Flash ON time for simulation
parameter FLASH_OFF_INIT = 5-1;  // Flash OFF time for simulation
parameter FLASH_COUNT_INIT = 3-1; // Flash count for simulation
`endif

reg [15:0] cycle_value;      // local copy of 'cycle' input when load trigger
reg [15:0] period_value;     // local copy of 'period' input when load trigger
reg [15:0] step_value;       // local copy of 'step' input when load trigger
reg [15:0] top_value;        // local copy of 'top' input when load trigger
reg [15:0] bottom_value;     // local copy of 'bottom' input when load trigger
reg [15:0] flash_on_value;   // local copy of 'flash_on_time' input when load trigger
reg [15:0] flash_off_value;  // local copy of 'flash_off_time' input when load trigger
reg [7:0] flash_count_value; // local copy of 'flash_count' input when load trigger
reg [1:0] mode_value;        // local copy of 'mode' input when load trigger

reg [15:0] pwm_counter;      // counter for pwm generation
reg [15:0] pwm_duty;         // current pwm duty cycle, when pwm_counter<pwm_duty, out=1; otherwise out=0. every period_value pwm cycles, change pwm_duty once
reg [15:0] period_count;     // elapsed pwm cycles before change pwm_duty, when period_count=period_value, change pwm_duty (+ or -)
reg [15:0] pause_count;      // counter for pause time at top/bottom brightness
reg [15:0] flash_timer;      // timer for flash mode timing
reg [7:0] flash_counter;     // counter for flash cycles completed

reg newvalue;                // each 'load' RE update value reg and set newvalue flag, FE clear newvalue flag
reg fade_dir;                // 0 = fade in, 1 = fade out 
reg pausing;                 // 1 = currently in pause state (at top or bottom)
reg flash_state;             // 0 = flash OFF, 1 = flash ON
reg flash_active;            // 1 = flash mode is active and counting

always @(posedge sysclk) begin
    if (!rstn) begin
        newvalue = 0;
        fade_dir = 0;
        cycle_value = CYCLE_INIT;
        period_value = PERIOD_INIT;
        step_value = STEP_INIT;
        top_value = TOP_INIT;
        bottom_value = BOT_INIT;
        flash_on_value = FLASH_ON_INIT;
        flash_off_value = FLASH_OFF_INIT;
        flash_count_value = FLASH_COUNT_INIT;
        mode_value = 2'b00;  // Default to ALL OFF mode
        pwm_duty = 0;
        period_count = 0;
        pwm_counter = 0;
        pause_count = 0;
        pausing = 0;
        flash_timer = 0;
        flash_counter = 0;
        flash_state = 0;
        flash_active = 0;
        out = 0;
    end
    else begin
        if (load && (!newvalue)) begin   // load new value to xxxx_value reg from interface
            cycle_value = cycle;
            period_value = period;
            step_value = step;
            top_value = top;
            bottom_value = bottom;
            flash_on_value = flash_on_time;
            flash_off_value = flash_off_time;
            flash_count_value = flash_count;
            mode_value = mode;
            newvalue = 1;
            // Reset flash mode when new parameters are loaded
            if (mode == 2'b10) begin  // Flash mode
                flash_timer = 0;
                flash_counter = 0;
                flash_state = 1;  // Start with ON state
                flash_active = 1;
            end else begin
                flash_active = 0;
            end
        end
        else if ((!load) && newvalue)   // after load clear, clear newvalue flag
            newvalue = 0;
        
        // Mode control logic
        case (mode_value)
            2'b00: out = 0;  // ALL OFF
            2'b01: begin     // Breath mode
                if (pwm_counter < pwm_duty)     // from 0 to pwm_duty, out=1, if pwm_duty=0, keep out=0
                    out = 1;
                else                            // from pwm_duty to cycle_value, out=0
                    out = 0;
            end
            2'b10: begin     // Flash mode
                if (flash_active) begin
                    if (flash_state)
                        out = 1;  // Flash ON
                    else
                        out = 0;  // Flash OFF
                end else begin
                    out = 0;  // Flash completed
                end
            end
            2'b11: out = 1;  // ALL ON
            default: out = 0;
        endcase
            
        // PWM counter - always running for timing reference
        if (((pwm_counter + 1'b1) > pwm_counter) && (pwm_counter < cycle_value))    // normal count
            pwm_counter = pwm_counter + 1'b1;  
        else begin                   // overflow, fold back to 0, current pwm cycle finished
            pwm_counter = 0;
            
            // Flash mode timing logic
            if (mode_value == 2'b10 && flash_active) begin
                if (flash_state) begin  // Currently ON
                    if (flash_timer < flash_on_value) begin
                        flash_timer = flash_timer + 1;
                    end else begin
                        flash_timer = 0;
                        flash_state = 0;  // Switch to OFF
                    end
                end else begin  // Currently OFF
                    if (flash_timer < flash_off_value) begin
                        flash_timer = flash_timer + 1;
                    end else begin
                        flash_timer = 0;
                        flash_state = 1;  // Switch to ON
                        flash_counter = flash_counter + 1;
                        // Check if flash count completed
                        if (flash_counter >= flash_count_value) begin
                            flash_active = 0;  // Stop flashing
                        end
                    end
                end
            end
            
            // Breathing mode logic - only execute when in breath mode
            if (mode_value == 2'b01) begin
                // Handle pausing at top or bottom brightness
                if (pausing) begin
                    // Check which pause we're in (top or bottom) and use appropriate counter
                    if ((fade_dir == 0 && pause_count < top_value) || 
                        (fade_dir == 1 && pause_count < bottom_value)) begin
                        pause_count = pause_count + 1;   // Continue pausing
                    end
                    else begin
                        pause_count = 0;
                        pausing = 0;   // End of pause
                        fade_dir = ~fade_dir;   // Change direction after pause completes
                    end
                end
                // Normal duty cycle update logic when not pausing
                else if (fade_dir == 0) begin    // fade in, pwm duty increase direction
                    if (period_count < period_value) begin
                        period_count = period_count + 1;
                    end
                    else begin
                        period_count = 0;
                        if ((pwm_duty >= cycle_value) || (pwm_duty + step_value + 1 < pwm_duty)) begin  // check overflow
                            pwm_duty = cycle_value;
                            if (top_value > 0) begin
                                pausing = 1;     // Start pausing at top brightness
                                pause_count = 0;
                                // Don't change fade_dir yet, will change after pause
                            end
                            else
                                fade_dir = 1;    // No pause needed, change direction immediately
                        end
                        else
                            pwm_duty = pwm_duty + step_value + 1;   // increase pwm_duty when fade in
                    end
                end
                else begin               // fade_dir = 1, fade out direction
                    if (period_count < period_value) begin
                        period_count = period_count + 1;
                    end
                    else begin
                        period_count = 0;
                        if (pwm_duty > step_value + 1)
                            pwm_duty = pwm_duty - step_value - 1;   // decrease pwm_duty to fade out
                        else begin
                            pwm_duty = 0;
                            if (bottom_value > 0) begin
                                pausing = 1;     // Start pausing at bottom brightness
                                pause_count = 0;
                                // Don't change fade_dir yet, will change after pause
                            end
                            else
                                fade_dir = 0;    // No pause needed, change direction immediately
                        end
                    end
                end
            end
        end
    end
end

endmodule
