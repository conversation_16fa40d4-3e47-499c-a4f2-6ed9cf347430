`timescale 1ns / 1ps
`define SIMULATION

module breathLED_tb;

// Inputs
reg sysclk;
reg rstn;
reg [1:0] mode;
reg load;
reg [15:0] cycle;
reg [15:0] period;
reg [15:0] step;
reg [15:0] top;
reg [15:0] bottom;
reg [15:0] flash_on_time;
reg [15:0] flash_off_time;
reg [7:0] flash_count;

// Outputs
wire out;

// Instantiate the Unit Under Test (UUT)
breathLED uut (
    .sysclk(sysclk), 
    .rstn(rstn), 
    .out(out), 
    .mode(mode),
    .load(load), 
    .cycle(cycle), 
    .period(period), 
    .step(step), 
    .top(top), 
    .bottom(bottom),
    .flash_on_time(flash_on_time),
    .flash_off_time(flash_off_time),
    .flash_count(flash_count)
);

// Clock generation
initial begin
    sysclk = 0;
    forever #4 sysclk = ~sysclk; // 125MHz clock (8ns period)
end

initial begin
    // Initialize Inputs
    rstn = 0;
    mode = 2'b00;
    load = 0;
    cycle = 99;      // 100 cycles for simulation
    period = 9;      // 10 periods
    step = 0;        // step of 1
    top = 9;         // 10 cycles pause at top
    bottom = 49;     // 50 cycles pause at bottom
    flash_on_time = 4;   // 5 cycles ON
    flash_off_time = 4;  // 5 cycles OFF
    flash_count = 2;     // 3 complete flash cycles

    // Wait for global reset
    #100;
    rstn = 1;
    #20;
    
    // Test ALL OFF mode
    $display("Testing ALL OFF mode");
    mode = 2'b00;
    load = 1;
    #20;
    load = 0;
    #1000;
    
    // Test ALL ON mode
    $display("Testing ALL ON mode");
    mode = 2'b11;
    load = 1;
    #20;
    load = 0;
    #1000;
    
    // Test Flash mode
    $display("Testing Flash mode");
    mode = 2'b10;
    load = 1;
    #20;
    load = 0;
    #5000;  // Wait for flash sequence to complete
    
    // Test Breath mode
    $display("Testing Breath mode");
    mode = 2'b01;
    load = 1;
    #20;
    load = 0;
    #10000; // Wait for breathing cycle
    
    $finish;
end

// Monitor output
initial begin
    $monitor("Time=%0t, mode=%b, out=%b", $time, mode, out);
end

endmodule
